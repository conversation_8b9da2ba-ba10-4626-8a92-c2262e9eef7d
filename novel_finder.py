import os
import time
import hashlib
import requests
import urllib3
import random
from utils import Logger

class NovelFinder:
    """小说查找器，负责从API接口查找小说"""

    def __init__(self):
        """初始化小说查找器

        Args:
            stories_dir (str): 小说文件夹路径
            api_url (str): API接口地址
            api_key (str): API接口密钥
        """
        self.stories_dir = "stories"
        self.api_url = "http://qiuov.cn:8082/search"
        self.api_key = "keyTestxiaoshuoBOOKxiaoshuoTestkey"

        # 禁用SSL警告（因为我们使用verify=False）
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 延迟初始化日志器，避免循环导入
        self._logger = None

        # 确保小说文件夹存在
        if not os.path.exists(self.stories_dir):
            os.makedirs(self.stories_dir)

    def _get_logger(self):
        """获取日志器实例，延迟初始化避免循环导入"""
        if self._logger is None:
            self._logger = Logger("NovelFinder")
            self._logger.debug("NovelFinder初始化完成")
            self._logger.debug(f"小说存储目录: {self.stories_dir}")
            self._logger.debug(f"API地址: {self.api_url}")
        return self._logger

    @property
    def logger(self):
        """日志器属性"""
        return self._get_logger()

    def _generate_sign(self, keyword, req_type):
        """生成签名

        Args:
            keyword (str): 关键词
            req_type (str): 请求类型

        Returns:
            str: 签名
        """
        timestamp = str(int(time.time()))
        sign_str = self.api_key + timestamp + keyword + req_type
        sign = hashlib.md5(sign_str.encode()).hexdigest()
        return timestamp, sign

    def search(self, keyword):
        """搜索包含关键词的小说

        Args:
            keyword (str): 搜索关键词

        Returns:
            list: 匹配的小说信息列表
        """
        if not keyword:
            self.logger.debug("搜索关键词为空，返回空列表")
            return []

        self.logger.debug(f"开始搜索小说，关键词: {keyword}")

        timestamp, sign = self._generate_sign(keyword, "search")

        params = {
            "name": keyword,
            "timestamp": timestamp,
            "type": "search",
            "sign": sign
        }

        self.logger.debug(f"搜索请求参数: {params}")

        try:
            self.logger.debug(f"发送搜索请求到: {self.api_url}")
            response = requests.get(self.api_url, params=params, verify=False, timeout=30)

            self.logger.debug(f"搜索响应状态码: {response.status_code}")
            self.logger.debug(f"搜索响应头: {dict(response.headers)}")

            if response.status_code == 200:
                response_data = response.json()
                self.logger.debug(f"搜索响应数据: {response_data}")

                if response_data.get("code") == 200:
                    items = response_data.get("data", {}).get("item", [])
                    self.logger.debug(f"搜索成功，找到 {len(items)} 个结果")
                    return items
                else:
                    self.logger.debug(f"搜索API返回错误代码: {response_data.get('code')}")
            else:
                self.logger.debug(f"搜索请求失败，HTTP状态码: {response.status_code}")
            return []
        except Exception as e:
            self.logger.debug(f"搜索请求异常: {type(e).__name__}: {e}")
            print(f"搜索失败: {e}")
            return []

    def display_search_results(self, novels, page=1, page_size=10):
        """格式化显示搜索结果

        Args:
            novels (list): 小说信息列表
            page (int): 当前页码
            page_size (int): 每页显示数量

        Returns:
            str: 格式化后的搜索结果
        """
        if not novels:
            return "未找到相关小说"

        total_pages = (len(novels) + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, len(novels))

        result = f"找到以下小说（第{page}/{total_pages}页）：\n"
        result += "序号 | 小说名称 | 格式 | 大小\n"
        result += "-----|---------|------|------\n"

        for i in range(start_idx, end_idx):
            novel = novels[i]
            name = novel.get("name_all", "")
            file_type = novel.get("icon", "未知").upper()
            file_size = novel.get("size", "未知")

            # 去除文件后缀名显示小说名
            for ext in ['.zip', '.txt', '.rar', '.pdf']:
                if name.lower().endswith(ext):
                    name = name[:-len(ext)]

            result += f"{i+1-start_idx} | {name} | {file_type} | {file_size}\n"

        result += "\n请回复数字选择要下载的小说，或者回复\"上一页\"/\"下一页\"翻页"

        return result

    def handle_browse_action(self, action, novels, current_page, page_size):
        """处理浏览操作（翻页、选择）

        Args:
            action (str): 用户操作，可以是数字或'上一页'/'下一页'
            novels (list): 小说信息列表
            current_page (int): 当前页码
            page_size (int): 每页显示数量

        Returns:
            tuple: (新页码, 选择的小说信息或None, 错误消息或None)
                - 如果是翻页操作，返回新的页码和None
                - 如果是选择小说，返回当前页码和小说信息
                - 如果是无效操作，返回当前页码和错误消息
        """
        total_pages = (len(novels) + page_size - 1) // page_size

        # 处理翻页命令
        if action == '下一页':
            if current_page < total_pages:
                return current_page + 1, None, None
            else:
                return current_page, None, "已经是最后一页了"

        if action == '上一页':
            if current_page > 1:
                return current_page - 1, None, None
            else:
                return current_page, None, "已经是第一页了"

        # 处理选择小说命令
        try:
            novel_index = int(action) - 1
            start_idx = (current_page - 1) * page_size
            actual_index = start_idx + novel_index

            if 0 <= novel_index < page_size and actual_index < len(novels):
                novel_info = novels[actual_index]
                return current_page, novel_info, None
            else:
                error_msg = f"请输入有效的小说序号 (1-{min(page_size, len(novels) - start_idx)})"
                return current_page, None, error_msg
        except ValueError:
            # 不是数字，也不是上一页/下一页，可能是其他命令
            # 不再显示错误提示，直接返回空字符串
            return current_page, None, ""

    def download_novel(self, novel_info):
        """下载小说

        Args:
            novel_info (dict): 小说信息对象，包含id、name_all等字段

        Returns:
            str: 小说文件路径，如果下载失败则返回None
        """
        if not novel_info:
            self.logger.debug("小说信息为空，无法下载")
            return None

        file_name = novel_info.get("name_all")
        if not file_name:
            self.logger.debug("小说文件名为空，无法下载")
            return None

        self.logger.debug(f"开始下载小说: {file_name}")
        self.logger.debug(f"小说信息: {novel_info}")

        # 确保文件名合法
        file_name = self._sanitize_filename(file_name)
        self.logger.debug(f"清理后的文件名: {file_name}")

        # 检查文件是否已存在
        file_path = os.path.join(self.stories_dir, file_name)
        if os.path.exists(file_path):
            # 文件已存在，直接返回路径
            self.logger.debug(f"文件已存在，直接返回: {file_path}")
            return file_path

        # 获取下载链接
        self.logger.debug("开始获取下载链接")
        download_url = self._get_download_url(novel_info)
        if not download_url:
            self.logger.debug("获取下载链接失败")
            return None

        self.logger.debug(f"获取到下载链接: {download_url}")

        # 下载文件
        try:
            self.logger.debug("开始下载文件")
            result = self._download_file(download_url, file_path)
            if result:
                self.logger.debug(f"文件下载成功: {result}")
            else:
                self.logger.debug("文件下载失败")
            return result
        except Exception as e:
            self.logger.debug(f"下载文件异常: {type(e).__name__}: {e}")
            print(f"下载失败: {e}")
            return None

    def _get_download_url(self, novel_info):
        """获取小说的真实下载链接

        Args:
            novel_info (dict): 小说信息对象，包含id等字段

        Returns:
            str: 真实下载链接，如果获取失败则返回None
        """
        novel_id = novel_info.get('id')
        if not novel_id:
            self.logger.debug("小说ID为空，无法获取下载链接")
            return None

        self.logger.debug(f"获取下载链接，小说ID: {novel_id}")

        timestamp, sign = self._generate_sign(novel_id, "getinfo")

        params = {
            "name": novel_id,
            "timestamp": timestamp,
            "type": "getinfo",
            "sign": sign
        }

        self.logger.debug(f"获取下载链接请求参数: {params}")

        try:
            # 获取下载链接
            self.logger.debug(f"发送获取下载链接请求到: {self.api_url}")
            response = requests.get(self.api_url, params=params, verify=False, timeout=30)

            self.logger.debug(f"获取下载链接响应状态码: {response.status_code}")
            self.logger.debug(f"获取下载链接响应头: {dict(response.headers)}")

            if response.status_code != 200:
                self.logger.debug(f"获取下载链接失败，HTTP状态码: {response.status_code}")
                print(f"获取下载链接失败，状态码: {response.status_code}")
                return None

            response_data = response.json()
            self.logger.debug(f"获取下载链接响应数据: {response_data}")

            initial_url = response_data.get("data")
            if not initial_url:
                self.logger.debug("获取下载链接失败，响应数据中没有下载链接")
                print("获取下载链接失败，返回数据为空")
                return None

            self.logger.debug(f"成功获取下载链接: {initial_url}")
            return initial_url

        except Exception as e:
            self.logger.debug(f"获取下载链接异常: {type(e).__name__}: {e}")
            print(f"获取下载链接失败: {e}")
            return None

    def _download_file(self, download_url, file_path):
        """下载文件并保存到指定路径

        Args:
            download_url (str): 下载链接
            file_path (str): 保存文件的路径

        Returns:
            str: 保存的文件路径，如果下载失败则返回None
        """
        self.logger.debug(f"开始下载文件，URL: {download_url}")
        self.logger.debug(f"目标文件路径: {file_path}")

        # 解析URL获取主机信息
        from urllib.parse import urlparse
        parsed_url = urlparse(download_url)
        self.logger.debug(f"下载主机: {parsed_url.hostname}")
        self.logger.debug(f"下载协议: {parsed_url.scheme}")
        self.logger.debug(f"下载端口: {parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)}")

        try:
            # 配置请求参数
            request_kwargs = {
                'stream': True,
                'verify': False,  # 禁用SSL证书验证
                'timeout': (30, 300),  # 连接超时30秒，读取超时300秒
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
            }

            self.logger.debug(f"请求配置: {request_kwargs}")

            # 使用流式下载处理大文件
            self.logger.debug("发起下载请求...")
            with requests.get(download_url, **request_kwargs) as response:
                self.logger.debug(f"下载响应状态码: {response.status_code}")
                self.logger.debug(f"下载响应头: {dict(response.headers)}")

                response.raise_for_status()

                # 获取文件大小信息
                content_length = response.headers.get('content-length')
                if content_length:
                    self.logger.debug(f"文件大小: {content_length} 字节")
                else:
                    self.logger.debug("无法获取文件大小信息")

                # 先保存为临时文件
                temp_file_path = file_path + ".temp"
                self.logger.debug(f"临时文件路径: {temp_file_path}")

                downloaded_size = 0
                with open(temp_file_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:  # 过滤掉保持连接的空块
                            f.write(chunk)
                            downloaded_size += len(chunk)

                self.logger.debug(f"文件下载完成，实际下载大小: {downloaded_size} 字节")

                # 文本文件尝试不同编码转换
                if file_path.lower().endswith('.txt') or not os.path.splitext(file_path)[1]:
                    self.logger.debug("检测到文本文件，尝试编码转换")
                    return self._convert_text_encoding(temp_file_path, file_path)

                # 其他类型文件直接重命名
                self.logger.debug("非文本文件，直接重命名")
                os.rename(temp_file_path, file_path)
                self.logger.debug(f"文件保存成功: {file_path}")
                return file_path

        except requests.exceptions.SSLError as e:
            self.logger.debug(f"SSL错误详细信息: {type(e).__name__}: {e}")
            self.logger.debug(f"SSL错误原因: {str(e)}")
            print(f"下载文件失败: {e}")
            self._cleanup_temp_files(file_path)
            return None
        except requests.exceptions.ConnectionError as e:
            self.logger.debug(f"连接错误详细信息: {type(e).__name__}: {e}")
            print(f"下载文件失败: {e}")
            self._cleanup_temp_files(file_path)
            return None
        except requests.exceptions.Timeout as e:
            self.logger.debug(f"超时错误详细信息: {type(e).__name__}: {e}")
            print(f"下载文件失败: {e}")
            self._cleanup_temp_files(file_path)
            return None
        except Exception as e:
            self.logger.debug(f"下载文件异常详细信息: {type(e).__name__}: {e}")
            self.logger.debug(f"异常详细描述: {str(e)}")
            print(f"下载文件失败: {e}")
            self._cleanup_temp_files(file_path)
            return None

    def _cleanup_temp_files(self, file_path):
        """清理临时文件

        Args:
            file_path (str): 目标文件路径
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.debug(f"删除部分下载文件: {file_path}")

            temp_file_path = file_path + ".temp"
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                self.logger.debug(f"删除临时文件: {temp_file_path}")
        except Exception as e:
            self.logger.debug(f"清理临时文件失败: {e}")

    def _convert_text_encoding(self, temp_file_path, target_file_path):
        """尝试不同的编码格式转换文本文件

        Args:
            temp_file_path (str): 临时文件路径
            target_file_path (str): 目标文件路径

        Returns:
            str: 保存的文件路径
        """
        self.logger.debug(f"开始文本编码转换，临时文件: {temp_file_path}")
        self.logger.debug(f"目标文件: {target_file_path}")

        # 尝试的编码列表
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'gb18030']
        self.logger.debug(f"尝试编码列表: {encodings}")

        # 读取文件内容
        try:
            with open(temp_file_path, 'rb') as f:
                content = f.read()

            file_size = len(content)
            self.logger.debug(f"读取文件内容，大小: {file_size} 字节")
        except Exception as e:
            self.logger.debug(f"读取临时文件失败: {e}")
            return None

        # 尝试不同编码
        for encoding in encodings:
            try:
                self.logger.debug(f"尝试使用编码: {encoding}")
                # 尝试解码
                decoded_content = content.decode(encoding)
                self.logger.debug(f"使用 {encoding} 编码解码成功，内容长度: {len(decoded_content)} 字符")

                # 如果解码成功，以UTF-8编码保存
                with open(target_file_path, 'w', encoding='utf-8') as f:
                    f.write(decoded_content)

                self.logger.debug(f"成功将文件从 {encoding} 转换为UTF-8编码")
                print(f"成功将文件从{encoding}转换为UTF-8编码")

                # 删除临时文件
                os.remove(temp_file_path)
                self.logger.debug(f"删除临时文件: {temp_file_path}")

                return target_file_path
            except UnicodeDecodeError as e:
                self.logger.debug(f"使用 {encoding} 编码解码失败: {e}")
                continue
            except Exception as e:
                self.logger.debug(f"使用 {encoding} 编码处理文件时出错: {e}")
                continue

        # 如果所有编码都失败，保留原始文件
        self.logger.debug("所有编码尝试都失败，保留原始文件")
        try:
            os.rename(temp_file_path, target_file_path)
            self.logger.debug(f"重命名临时文件为目标文件: {target_file_path}")
            print("无法识别文件编码，保留原始文件")
            return target_file_path
        except Exception as e:
            self.logger.debug(f"重命名临时文件失败: {e}")
            return None

    def _sanitize_filename(self, filename):
        """清理文件名，移除不合法字符

        Args:
            filename (str): 原始文件名

        Returns:
            str: 清理后的文件名
        """
        self.logger.debug(f"清理文件名，原始文件名: {filename}")

        # 移除Windows不允许的文件名字符
        invalid_chars = r'<>:"/\|?*'
        original_filename = filename
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        if filename != original_filename:
            self.logger.debug(f"文件名已清理，清理后: {filename}")
        else:
            self.logger.debug("文件名无需清理")

        return filename

    def get_novel_path(self, novel_name):
        """获取小说文件的完整路径

        Args:
            novel_name (str): 小说文件名

        Returns:
            str: 小说文件的完整路径，如果文件不存在则返回None
        """
        self.logger.debug(f"查找小说文件路径，文件名: {novel_name}")

        novel_path = os.path.join(self.stories_dir, novel_name)
        self.logger.debug(f"完整路径: {novel_path}")

        if os.path.exists(novel_path):
            self.logger.debug(f"文件存在，返回路径: {novel_path}")
            return novel_path
        else:
            self.logger.debug("文件不存在，返回None")
            return None