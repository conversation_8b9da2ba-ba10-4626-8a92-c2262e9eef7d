#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试虚标数量功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from novel_finder import NovelFinder

def test_fake_count():
    """测试虚标数量功能"""
    print("=== 测试虚标数量功能 ===")
    
    # 创建NovelFinder实例
    finder = NovelFinder()
    
    # 测试小于等于10本的情况
    print("\n1. 测试实际数量<=10的情况:")
    for count in [1, 5, 10]:
        fake_count = finder.generate_fake_count(count)
        print(f"   实际数量: {count}, 虚标数量: {fake_count}")
        assert fake_count == count, f"小于等于10本时应返回真实数量，但得到了{fake_count}"
    
    # 测试大于10本的情况
    print("\n2. 测试实际数量>10的情况:")
    for count in [11, 15, 20, 50]:
        fake_count = finder.generate_fake_count(count)
        print(f"   实际数量: {count}, 虚标数量: {fake_count}")
        assert 300 <= fake_count <= 800, f"大于10本时应返回300-800之间的随机数，但得到了{fake_count}"
        assert fake_count != count, f"虚标数量不应等于实际数量"
    
    print("\n✅ 虚标数量功能测试通过！")

def test_display_with_fake_count():
    """测试带虚标的显示功能"""
    print("\n=== 测试带虚标的显示功能 ===")
    
    # 创建NovelFinder实例
    finder = NovelFinder()
    
    # 模拟小说数据（12本小说）
    novels = []
    for i in range(12):
        novels.append({
            "name_all": f"测试小说{i+1}.txt",
            "icon": "txt",
            "size": "1.2MB"
        })
    
    print(f"\n实际小说数量: {len(novels)}")
    
    # 生成虚标数量
    fake_count = finder.generate_fake_count(len(novels))
    print(f"虚标数量: {fake_count}")
    
    # 测试第一页显示
    print("\n--- 第1页显示 ---")
    result = finder.display_search_results(novels, page=1, page_size=10, fake_count=fake_count)
    print(result)
    
    # 测试第二页显示（应该显示剩余的2本）
    print("\n--- 第2页显示 ---")
    result = finder.display_search_results(novels, page=2, page_size=10, fake_count=fake_count)
    print(result)
    
    # 测试第三页显示（应该循环显示前面的小说）
    print("\n--- 第3页显示（循环） ---")
    result = finder.display_search_results(novels, page=3, page_size=10, fake_count=fake_count)
    print(result)
    
    print("\n✅ 带虚标的显示功能测试完成！")

def test_browse_action_with_fake_count():
    """测试带虚标的浏览操作"""
    print("\n=== 测试带虚标的浏览操作 ===")
    
    # 创建NovelFinder实例
    finder = NovelFinder()
    
    # 模拟小说数据（12本小说）
    novels = []
    for i in range(12):
        novels.append({
            "name_all": f"测试小说{i+1}.txt",
            "icon": "txt",
            "size": "1.2MB",
            "id": f"novel_{i+1}"
        })
    
    fake_count = finder.generate_fake_count(len(novels))
    total_pages = (fake_count + 10 - 1) // 10
    
    print(f"实际小说数量: {len(novels)}")
    print(f"虚标数量: {fake_count}")
    print(f"虚标总页数: {total_pages}")
    
    # 测试翻页操作
    print("\n--- 测试翻页操作 ---")
    current_page = 1
    
    # 测试下一页
    new_page, novel_info, error_msg = finder.handle_browse_action(
        "下一页", novels, current_page, 10, fake_count
    )
    print(f"从第{current_page}页翻到下一页: 第{new_page}页")
    
    # 测试选择小说（在虚标模式下）
    print("\n--- 测试选择小说 ---")
    current_page = 3  # 测试第3页（应该是循环显示）
    
    # 选择第1个小说
    new_page, novel_info, error_msg = finder.handle_browse_action(
        "1", novels, current_page, 10, fake_count
    )
    
    if novel_info:
        print(f"在第{current_page}页选择第1个小说: {novel_info.get('name_all')}")
    else:
        print(f"选择失败: {error_msg}")
    
    print("\n✅ 带虚标的浏览操作测试完成！")

if __name__ == "__main__":
    try:
        test_fake_count()
        test_display_with_fake_count()
        test_browse_action_with_fake_count()
        print("\n🎉 所有测试都通过了！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
